name: ivent_app
description: A new Flutter project.
publish_to: "none"
version: 1.0.0+4

environment:
  sdk: ">=3.0.5 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.2
  flutter_svg: ^2.1.0
  get: ^4.6.6
  sliding_up_panel: ^2.0.0+1
  calendar_date_picker2: ^2.0.0
  dio: ^5.4.2+1
  flutter_verification_code: ^1.1.6
  google_fonts: ^6.2.1
  shared_preferences: ^2.3.2
  intl: ^0.20.2
  pretty_dio_logger: ^1.3.1
  logger: ^2.2.0
  geolocator: ^14.0.0
  permission_handler: ^12.0.0+1
  mapbox_maps_flutter: ^2.7.0
  flutter_map: ^8.1.1
  latlong2: ^0.9.1
  rxdart: ^0.28.0
  flutter_launcher_icons: ^0.14.3
  video_player: ^2.9.5
  visibility_detector: ^0.4.0+2
  flutter_cache_manager: ^3.4.1
  http: ^1.4.0
  cached_network_image: ^3.4.1
  camera: ^0.11.1
  path_provider: ^2.1.5
  photo_manager: ^3.6.4
  equatable: ^2.0.7
  dartz: ^0.10.1
  connectivity_plus: ^6.1.4
  flutter_dotenv: ^5.2.1
  flutter_native_splash: ^2.4.6
  sentry_flutter: ^8.14.2
  share_plus: ^11.0.0
  image_picker: ^1.1.2
  flutter_phoenix: ^1.1.1
  flutter_pdfview: ^1.3.2
  path: ^1.8.3
  url_launcher: ^6.2.5
  flutter_contacts: ^1.1.9+2

dev_dependencies:
  sentry_dart_plugin: ^2.4.1
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  build_runner: ^2.4.9
  flutter_lints: ^5.0.0
  json_serializable: ^6.9.5

flutter:
  uses-material-design: true

  fonts:
    - family: SF Pro Display
      fonts:
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Ultralight.otf
          weight: 100
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Thin.otf
          weight: 200
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Light.otf
          weight: 300
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Regular.otf
          weight: 400
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Medium.otf
          weight: 500
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Semibold.otf
          weight: 600
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Bold.otf
          weight: 700
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Heavy.otf
          weight: 800
        - asset: assets/fonts/SFProFonts/SF-Pro-Display-Black.otf
          weight: 900

  assets:
    - .env
    - assets/
    - assets/icons/
    - assets/icons/Arrow/
    - assets/icons/Calendar/
    - assets/icons/Communication/
    - assets/icons/Edit/
    - assets/icons/Environment/
    - assets/icons/File/
    - assets/icons/Interface/
    - assets/icons/Media/
    - assets/icons/Menu/
    - assets/icons/Navigation/
    - assets/icons/Shape/
    - assets/icons/Social/
    - assets/icons/System/
    - assets/icons/User/
    - assets/icons/Warning/

flutter_launcher_icons:
  android: true
  image_path: "ivent.jpg"

flutter_native_splash:
  color: "#01AEBE"
  image: assets/splash_icon.png
  android_12:
    image: assets/splash_icon.png
    icon_background_color: "#01AEBE"
  web: false
  ios: true
  android: true

  # Control the size of the splash image
  android_gravity: center
  ios_content_mode: center
  fullscreen: true

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  project: flutter
  org: bogazici-university-0y
