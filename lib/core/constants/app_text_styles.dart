import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';

/// Centralized text styles following a systematic approach
///
/// Naming convention: {weight}{size}{color}
/// Example: boldLarge01AEBE = bold + large + blue color
class AppTextStyles {
  AppTextStyles._();

  // Base text styles
  static const TextStyle _baseStyle = TextStyle(
    fontFamily: 'SF Pro Display',
    height: 1.2,
    letterSpacing: 0,
    overflow: TextOverflow.ellipsis,
  );

  // Font weights
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight bold = FontWeight.w700;

  // Font sizes
  static const double size10 = 10; // tiny
  static const double size12 = 12; // small
  static const double size14 = 14; // body
  static const double size16 = 16; // subtitle
  static const double size18 = 18; // title
  static const double size20 = 20; // h4
  static const double size24 = 24; // h3
  static const double size28 = 28; // h2
  static const double size32 = 32; // h1

  static TextStyle get size10Regular => _baseStyle.copyWith(
        fontSize: size10,
        fontWeight: regular,
        color: AppColors.textPrimary,
      );

  static TextStyle get size12Regular => _baseStyle.copyWith(
        fontSize: size12,
        fontWeight: regular,
        color: AppColors.textPrimary,
      );

  static TextStyle get size16Regular => _baseStyle.copyWith(
        fontSize: size16,
        fontWeight: regular,
        color: AppColors.textPrimary,
      );

  static TextStyle get size12Medium => _baseStyle.copyWith(
        fontSize: size12,
        fontWeight: medium,
        color: AppColors.textPrimary,
      );

  static TextStyle get size12Bold => _baseStyle.copyWith(
        fontSize: size12,
        fontWeight: bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get size14Regular => _baseStyle.copyWith(
        fontSize: size14,
        fontWeight: regular,
        color: AppColors.textPrimary,
      );

  static TextStyle get size14Medium => _baseStyle.copyWith(
        fontSize: size14,
        fontWeight: medium,
        color: AppColors.textPrimary,
      );

  static TextStyle get size14Bold => _baseStyle.copyWith(
        fontSize: size14,
        fontWeight: bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get size16Medium => _baseStyle.copyWith(
        fontSize: size16,
        fontWeight: medium,
        color: AppColors.textPrimary,
      );

  static TextStyle get size16Bold => _baseStyle.copyWith(
        fontSize: size16,
        fontWeight: bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get size20Regular => _baseStyle.copyWith(
        fontSize: size20,
        fontWeight: regular,
        color: AppColors.textPrimary,
      );

  static TextStyle get size20Medium => _baseStyle.copyWith(
        fontSize: size20,
        fontWeight: medium,
        color: AppColors.textPrimary,
      );

  static TextStyle get size20Bold => _baseStyle.copyWith(
        fontSize: size20,
        fontWeight: bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get size24Bold => _baseStyle.copyWith(
        fontSize: size24,
        fontWeight: bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get size32Bold => _baseStyle.copyWith(
        fontSize: size32,
        fontWeight: bold,
        color: AppColors.textPrimary,
      );

  static TextStyle get size10RegularWhite => size10Regular.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size10RegularTextSecondary => size10Regular.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size12RegularTextSecondary => size12Regular.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size12RegularTextTertiary => size12Regular.copyWith(
        color: AppColors.textTertiary,
      );

  static TextStyle get size12RegularWhite => size12Regular.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size12MediumTextSecondary => size12Medium.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size12BoldPrimary => size12Bold.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size14BoldPrimary => size14Bold.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size14BoldWhite => size14Bold.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size14MediumTextSecondary => size14Medium.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size14RegularTextSecondary => size14Regular.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size14BoldTextSecondary => size14Bold.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size16RegularTextSecondary => size16Regular.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size16RegularPrimary => size16Regular.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size16MediumTextSecondary => size16Medium.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size16MediumPrimary => size16Medium.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size16BoldTextSecondary => size16Bold.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size16MediumWhite => size16Medium.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size16BoldPrimary => size16Bold.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size20MediumWhite => size20Medium.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size20BoldPrimary => size20Bold.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size20BoldWhite => size20Bold.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size24BoldTextSecondary => size24Bold.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size24BoldPrimary => size24Bold.copyWith(
        color: AppColors.primary,
      );

  static TextStyle get size24BoldWhite => size24Bold.copyWith(
        color: AppColors.white,
      );

  static TextStyle get size32BoldTextSecondary => size32Bold.copyWith(
        color: AppColors.textSecondary,
      );

  static TextStyle get size32BoldWhite => size32Bold.copyWith(
        color: AppColors.white,
      );

  static TextStyle get buttonTextMedium => size16MediumWhite;

  // Helper method to create custom styles
  static TextStyle custom({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    return _baseStyle.copyWith(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      height: height,
      letterSpacing: letterSpacing,
    );
  }
}
