import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_text_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/routes/ivent_create.dart';

class SharedButtons {
  SharedButtons._();

  static IaRoundedButton longBar({
    Key? key,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    required bool isEnabled,
    bool isPrimary = true,
    bool isLoading = false,
    required String text,
    String? leadingIconPath,
    String? trailingIconPath,
  }) {
    return IaRoundedButton(
      key: key,
      margin: margin,
      width: double.maxFinite,
      height: AppDimensions.buttonHeightLongBar,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isEnabled
          ? isPrimary
              ? AppColors.primary
              : AppColors.secondary
          : AppColors.darkGrey,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium,
      leading: leadingIconPath != null ? IaSvgIcon(iconPath: leadingIconPath) : null,
      trailing: trailingIconPath != null ? IaSvgIcon(iconPath: trailingIconPath) : null,
      child: isLoading ? const Center(child: CircularProgressIndicator(color: AppColors.white, strokeWidth: 2)) : null,
    );
  }

  static IaIconButton backButton({
    Key? key,
    VoidCallback? onTap,
    Color? color,
  }) {
    return IaIconButton(
      onPressed: () => onTap != null ? onTap() : Get.back(),
      buttonSize: AppDimensions.buttonSizeBack,
      iconPath: AppAssets.chevronLeft,
      iconSize: AppDimensions.buttonSizeBack,
      iconColor: color ?? AppColors.darkGrey,
    );
  }

  static IaIconButton moreHorizontalDark({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      iconPath: AppAssets.moreHorizontal,
      iconColor: AppColors.grey800,
    );
  }

  static IaIconButton moreHorizontalLight({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      iconPath: AppAssets.moreHorizontal,
      iconColor: AppColors.white,
    );
  }

  static IaIconButton moreVertical({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      iconPath: AppAssets.moreVertical,
      iconColor: AppColors.mediumGrey,
    );
  }

  static IaIconButton rightArrowDark({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.chevronRight,
      iconSize: AppDimensions.defaultIconButtonSize * 2 / 3,
      iconColor: AppColors.grey800,
    );
  }

  static IaIconButton rightArrowGrey({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.caretRightSM,
      iconSize: AppDimensions.defaultIconButtonSize * 2 / 3,
      iconColor: AppColors.grey400,
    );
  }

  static IaIconButton rightArrowLight({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.caretRightSM,
      iconSize: AppDimensions.defaultIconButtonSize * 2 / 3,
      iconColor: AppColors.white,
    );
  }

  static Widget addFriendListTile({
    Key? key,
    VoidCallback? onTap,
    required UserRelationshipStatusEnum relationshipStatus,
  }) {
    if (relationshipStatus == UserRelationshipStatusEnum.pending) {
      return IaTextButton(
        key: key,
        onPressed: onTap,
        text: 'İstek Gönderildi',
        textStyle: AppTextStyles.size16MediumTextSecondary,
      );
    }
    final isFriend = relationshipStatus == UserRelationshipStatusEnum.accepted;
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: isFriend ? AppAssets.userCheck : AppAssets.userAdd,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: isFriend ? AppColors.darkGrey : AppColors.primary,
    );
  }

  static Widget followAccountListTile({
    Key? key,
    VoidCallback? onTap,
    required bool isFollowing,
  }) {
    return IaTextButton(
      key: key,
      onPressed: onTap,
      text: isFollowing ? 'Takip Ediliyor' : 'Takip Et',
      textStyle: isFollowing ? AppTextStyles.size16MediumTextSecondary : AppTextStyles.size16MediumPrimary,
    );
  }

  static IaCircularButton createIventButton({
    Key? key,
    required double buttonSize,
  }) {
    return IaCircularButton(
      key: key,
      onPressed: () => Get.toNamed(IventCreateRoutes.IVENT_OLUSTUR_KATEGORI_SECINIZ),
      buttonSize: buttonSize,
      backgroundColor: AppColors.secondary,
      iconPath: AppAssets.addPlus,
      iconSize: buttonSize * 1.2,
    );
  }

  static IaCircularButton checkBox({
    Key? key,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return IaCircularButton(
      key: key,
      buttonSize: AppDimensions.buttonSizeCheckBox,
      backgroundColor: isSelected ? AppColors.primary : AppColors.lightGrey,
      border: Border.all(color: AppColors.mediumGrey, width: 1),
      onPressed: onTap,
    );
  }

  static IaCircularButton mapLocation({
    Key? key,
    VoidCallback? onTap,
  }) {
    return IaCircularButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      backgroundColor: AppColors.black.withValues(alpha: 0.2),
      iconPath: AppAssets.navigation,
      iconSize: AppDimensions.defaultIconButtonSize * 2 / 3,
      iconColor: AppColors.white,
    );
  }
}
