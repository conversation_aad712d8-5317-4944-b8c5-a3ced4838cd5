import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';

class IaFloatingActionButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isEnabled;
  final bool isPrimary;
  final bool isLoading;
  final String? leadingIconPath;
  final String? trailingIconPath;

  const IaFloatingActionButton({
    super.key,
    required this.text,
    required this.onPressed,
    required this.isEnabled,
    this.isPrimary = true,
    this.isLoading = false,
    this.leadingIconPath,
    this.trailingIconPath,
  });

  @override
  Widget build(BuildContext context) {
    return SharedButtons.longBar(
      onTap: isEnabled && !isLoading ? onPressed : () {},
      margin: const EdgeInsets.all(AppDimensions.padding20),
      isEnabled: isEnabled,
      isPrimary: isPrimary,
      isLoading: isLoading,
      text: text,
      leadingIconPath: leadingIconPath,
      trailingIconPath: trailingIconPath,
    );
  }
}
