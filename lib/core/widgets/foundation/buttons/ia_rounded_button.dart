import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';

/// A customizable rounded button with flexible content layout.
///
/// This widget creates a button with rounded corners that can display text along
/// with optional leading and trailing widgets. It wraps an [IaRoundedContainer]
/// with button-specific behavior and styling.
///
/// The button supports various customization options including margins, padding,
/// dimensions, colors, and content arrangement.
class IaRoundedButton extends StatelessWidget {
  /// Optional margin around the button.
  final EdgeInsetsGeometry? margin;

  /// Optional padding inside the button.
  final EdgeInsetsGeometry? padding;

  /// Optional fixed width for the button.
  final double? width;

  /// Optional fixed height for the button.
  final double? height;

  /// Corner radius of the button. Defaults to 0 (square corners).
  final double roundness;

  /// Callback function executed when the button is tapped.
  final VoidCallback? onTap;

  /// Callback function executed when the button is long-pressed.
  final VoidCallback? onLongPress;

  /// Background color of the button.
  final Color color;

  /// Optional border decoration for the button.
  final Border? border;

  /// Optional shadow effects for the button.
  final List<BoxShadow>? boxShadow;

  /// Optional text to display in the button.
  final String? text;

  /// Optional text style for the button text. Defaults to AppTextStyles.size14Bold.
  final TextStyle? textStyle;

  /// Optional widget to display before the text.
  final Widget? leading;

  /// Optional widget to display after the text.
  final Widget? trailing;

  /// Whether the button should expand to fill available width.
  /// If true, the content row will use MainAxisSize.max, otherwise MainAxisSize.min.
  /// Defaults to true.
  final bool expand;

  /// Optional child widget to display inside the button.
  /// If provided, [text], [leading], [trailing] and [expand] are ignored.
  /// Defaults to null.
  final Widget? child;

  /// Creates a rounded button with customizable properties.
  ///
  /// The [color] parameter is required to set the background color.
  /// Other parameters are optional for further customization.
  const IaRoundedButton({
    super.key,
    this.margin,
    this.padding,
    this.width,
    this.height,
    this.roundness = 0,
    this.onTap,
    this.onLongPress,
    required this.color,
    this.border,
    this.boxShadow,
    this.text,
    this.textStyle,
    this.leading,
    this.trailing,
    this.expand = true,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      margin: margin,
      padding: padding,
      width: width,
      height: height,
      roundness: roundness,
      onTap: onTap,
      onLongPress: onLongPress,
      color: color,
      border: border,
      boxShadow: boxShadow,
      child: child ?? _buildChild,
    );
  }

  Widget? get _buildChild {
    return Row(
      mainAxisSize: expand ? MainAxisSize.max : MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (leading != null) leading!,
        if (leading != null) const SizedBox(width: AppDimensions.padding4),
        if (text != null) Text(text!, style: textStyle ?? AppTextStyles.size14Bold),
        if (trailing != null) const SizedBox(width: AppDimensions.padding4),
        if (trailing != null) trailing!,
      ],
    );
  }
}
