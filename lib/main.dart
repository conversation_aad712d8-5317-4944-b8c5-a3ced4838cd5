import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:get/get.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/routes/app_pages.dart';
import 'package:ivent_app/routes/global_bindings.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize date formatting for Turkish locale
  await initializeDateFormatting('tr_TR', null);
  Intl.defaultLocale = 'tr_TR';

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  await dotenv.load(fileName: '.env');

  debugPrint('🚀 [Main] Starting app on Platform: ${Platform.operatingSystem}');

  // Set Mapbox access token globally
  final mapboxToken = dotenv.env['MAPBOX_ACCESS_TOKEN'];
  if (mapboxToken != null && mapboxToken.isNotEmpty) {
    MapboxOptions.setAccessToken(mapboxToken);
    debugPrint('🔑 [Mapbox] Using token: ${mapboxToken.substring(0, 20)}...');
  } else {
    debugPrint('❌ [Mapbox] Token not found in .env file');
  }

  // Initialize AuthService
  await Get.putAsync(() => AuthService().init());

  // Set up Sentry error tracking
  final sentryDsn = dotenv.env['SENTRY_DSN'];
  if (sentryDsn != null && sentryDsn.isNotEmpty) {
    await SentryFlutter.init(
      (options) {
        options.dsn = sentryDsn;
        options.tracesSampleRate = 1.0;
      },
      appRunner: () => runApp(Phoenix(child: SentryWidget(child: IventApp()))),
    );
    debugPrint('🔔 [Sentry] Sentry initialized with DSN: ${sentryDsn.substring(0, 20)}...');
  } else {
    debugPrint('❌ [Sentry] Sentry DSN not found in .env file');
    runApp(Phoenix(child: SentryWidget(child: IventApp())));
  }
}

class IventApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(colorScheme: AppColors.lightColorScheme),
      darkTheme: ThemeData(colorScheme: AppColors.darkColorScheme),
      themeMode: ThemeMode.light,
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('tr', 'TR'),
        Locale('en', 'US'),
      ],
      locale: const Locale('tr', 'TR'),
      initialBinding: GlobalBindings(),
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,
      builder: (context, child) {
        return VersionBanner(child: child ?? const SizedBox.shrink());
      },
    );
  }
}

class VersionBanner extends StatelessWidget {
  final Widget child;

  const VersionBanner({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        Positioned(
          right: 4,
          bottom: 4,
          child: Material(
            color: Colors.transparent,
            child: FutureBuilder<PackageInfo>(
              future: PackageInfo.fromPlatform(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) return const SizedBox.shrink();
                final version = '${snapshot.data!.version}+${snapshot.data!.buildNumber}';
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black26,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    version,
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
