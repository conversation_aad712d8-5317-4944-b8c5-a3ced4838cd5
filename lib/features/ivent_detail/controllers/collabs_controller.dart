import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/routes/ivent_detail.dart';
import 'package:ivent_app/shared/controllers/base_search_bar_controller.dart';

/// Controller for managing iVent collaborators
///
/// Handles searching and displaying collaborators (paydaşlar) for an iVent.
/// Provides search functionality and navigation to the collaborators page.
/// Integrates with BaseSearchBarController for search input management.
class CollabsController extends BaseIventDetailsController {
  // Controllers
  late final BaseSearchBarController baseSearchBarController;

  // Reactive state
  final _collabsResult = Rxn<SearchCollabsReturn>();

  // Constructor
  CollabsController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  // Getters
  SearchCollabsReturn? get collabsResult => _collabsResult.value;
  TextEditingController get textEditingController => baseSearchBarController.textEditingController;
  String get searchText => baseSearchBarController.text;
  bool get isSearching => baseSearchBarController.isSearching;
  bool get isQueryEmpty => searchText.isEmpty;
  bool get isResultsEmpty => collabsResult?.collabs.isEmpty ?? true;

  // Lifecycle methods

  @override
  Future<void> initController() async {
    super.initController();
    baseSearchBarController = Get.put(
      BaseSearchBarController((q) => _searchCollabs(q: q)),
      tag: 'CollabsController',
    );
  }

  @override
  void closeController() {
    Get.delete<BaseSearchBarController>(tag: 'CollabsController');
    super.closeController();
  }

  // Public methods

  /// Navigates to collaborators page and loads data if needed
  Future<void> getCollabsPage({String? q}) async {
    try {
      Get.toNamed(IventDetayRoutes.IVENT_DETAY_PAYDASLAR, arguments: state.iventId);

      // Load initial data if not already loaded
      if (collabsResult == null) {
        await _searchCollabs();
      }
    } catch (e) {
      handleError(e);
    }
  }

  // Private methods

  /// Searches for collaborators with optional query
  Future<void> _searchCollabs({String? q}) async {
    try {
      setLoading(true);

      _collabsResult.value = await authService.iventCollabsApi.searchCollabs(
        state.iventId,
        q: q,
      );

      setLoading(false);
    } catch (e) {
      handleError(e);
    }
  }
}
