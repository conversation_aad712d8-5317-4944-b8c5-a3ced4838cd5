import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/base_ivent_details_controller.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';

/// Controller for managing iVent information and basic operations
///
/// Handles loading iVent details, favorite operations, and basic iVent data
/// management. This controller is responsible for the main iVent information
/// displayed on the detail page.
class IventInfoController extends BaseIventDetailsController {
  // Reactive state
  final _iventPage = Rxn<GetIventPageByIventIdReturn>();
  final _isFavorited = false.obs;

  // Constructor
  IventInfoController(
    AuthService authService,
    IventDetailStateManager state,
    String iventId,
  ) : super(authService, state, iventId);

  // Getters
  GetIventPageByIventIdReturn? get iventPage => _iventPage.value;
  bool get isFavorited => _isFavorited.value;

  // Setters
  set iventPage(GetIventPageByIventIdReturn? value) => _iventPage.value = value;
  set isFavorited(bool value) => _isFavorited.value = value;

  // Lifecycle methods

  @override
  void initController() async {
    super.initController();
    await _loadIventData();
  }

  // Public methods

  /// Toggles the favorite status of the iVent
  Future<void> toggleIventFavorite() async {
    try {
      setLoading(true);

      if (isFavorited) {
        await authService.iventsApi.unfavoriteIventByIventId(iventId);
      } else {
        await authService.iventsApi.favoriteIventByIventId(iventId);
      }

      isFavorited = !isFavorited;
      setLoading(false);
    } catch (e) {
      handleError(e);
    }
  }

  /// Refreshes iVent data from the server
  Future<void> refreshIventData() async {
    await _loadIventData();
  }

  // Private methods

  /// Loads iVent data from the API
  Future<void> _loadIventData() async {
    try {
      setLoading(true);

      iventPage = await authService.iventsApi.getIventPageByIventId(iventId);

      if (iventPage != null) {
        isFavorited = iventPage!.isFavorited ?? false;
      }

      setLoading(false);
    } catch (e) {
      handleError(e);
    }
  }
}
