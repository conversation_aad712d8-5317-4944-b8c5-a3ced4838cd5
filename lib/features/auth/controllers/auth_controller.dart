import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/features/auth/controllers/base_auth_controller.dart';
import 'package:ivent_app/features/auth/controllers/sub_controllers/contacts_controller.dart';
import 'package:ivent_app/features/auth/controllers/sub_controllers/registration_controller.dart';
import 'package:ivent_app/features/auth/controllers/sub_controllers/validation_controller.dart';
import 'package:ivent_app/routes/auth.dart';

class AuthController extends BaseAuthController {
  AuthController(AuthService authService, AuthStateManager state) : super(authService, state);

  late final ValidationController validationController;
  late final RegistrationController registrationController;
  late final ContactsController contactsController;

  @override
  void initController() async {
    super.initController();
    validationController = Get.put(ValidationController(authService, state));
    registrationController = Get.put(RegistrationController(authService, state));
    contactsController = Get.put(ContactsController(authService, state));
  }

  @override
  void closeController() {
    Get.delete<ValidationController>();
    Get.delete<RegistrationController>();
    Get.delete<ContactsController>();
    super.closeController();
  }

  void goToPhonePage() => Get.toNamed(AuthRoutes.PHONE_PAGE);
  void goToValidatePhonePage() {
    Get.toNamed(AuthRoutes.VALIDATE_PHONE);
    authService.authApi.sendVerificationCode(
      SendVerificationCodeDto(phoneNumber: state.formattedPhoneNumber),
    );
  }

  void goToRegistrationHobbiesView() => Get.toNamed(AuthRoutes.REGISTRATION_HOBBIES_VIEW);
  void goToAccessPage() => Get.toNamed(AuthRoutes.ACCESS_PAGE);
  void goToContactsPage(bool accessGranted) => contactsController.goToContactsPage(accessGranted);
  void goToBottomNavBar() => Get.toNamed(AuthRoutes.APP_NAVIGATION);
}
