import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/features/auth/controllers/base_auth_controller.dart';

class RegistrationController extends BaseAuthController {
  RegistrationController(AuthService authService, AuthStateManager state) : super(authService, state);

  bool get isFullnameValid =>
      state.fullname.trim().length >= AuthValidationConstants.fullNameMinLength &&
      state.fullname.trim().length <= AuthValidationConstants.fullNameMaxLength;

  bool get areHobbiesValid => state.checkedHobbyIds.length >= AuthValidationConstants.minRequiredHobbies;
  bool get isRegistrationDataValid => isFullnameValid && areHobbiesValid && state.isPhoneNumberValid;
  bool get isRegistrationSuccessful => state.registerReturn != null;

  void addHobby(String hobbyId) {
    if (!state.checkedHobbyIds.contains(hobbyId)) {
      state.checkedHobbyIds.add(hobbyId);
    }
  }

  void removeHobby(String hobbyId) {
    state.checkedHobbyIds.remove(hobbyId);
  }

  void toggleHobby(String hobbyId) {
    if (state.checkedHobbyIds.contains(hobbyId)) {
      removeHobby(hobbyId);
    } else {
      addHobby(hobbyId);
    }
  }

  Future<void> registerUser() async {
    await runWithLoading(
      () async {
        if (!isRegistrationDataValid) {
          throw Exception(_getValidationErrorMessage());
        }

        final registerDto = RegisterDto(
          fullname: state.fullname,
          phoneNumber: state.formattedPhoneNumber,
          hobbyIds: state.checkedHobbyIds,
        );

        state.registerReturn = await authService.usersApi.register(registerDto);

        if (state.registerReturn != null) {
          await _createUserSession();
        } else {
          throw Exception('Kayıt işlemi başarısız oldu');
        }
      },
    );
  }

  Future<void> _createUserSession() async {
    final result = state.registerReturn!;

    final sessionUser = SessionUser(
      token: result.token,
      sessionId: result.userId,
      sessionRole: result.role,
      sessionUsername: result.username,
      sessionFullname: result.fullname,
      sessionAvatarUrl: result.avatarUrl,
    );

    await authService.login(sessionUser);
  }

  String _getValidationErrorMessage() {
    if (!state.isPhoneNumberValid) {
      return 'Geçersiz telefon numarası';
    }
    if (!isFullnameValid) {
      return 'Geçersiz ad soyad';
    }
    if (!areHobbiesValid) {
      return 'En az ${AuthValidationConstants.minRequiredHobbies} ilgi alanı seçmelisiniz';
    }
    return 'Kayıt bilgileri eksik veya hatalı';
  }
}
