import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/features/auth/controllers/base_auth_controller.dart';
import 'package:ivent_app/routes/auth.dart';

class ContactsController extends BaseAuthController {
  ContactsController(AuthService authService, AuthStateManager state) : super(authService, state);

  bool get hasContactsLoaded => state.getContactsReturn != null;
  int get pendingInvitationsCount => state.pendingContactIds.length;

  Future<void> goToContactsPage(bool accessGranted) async {
    await runWithLoading(
      () async {
        Get.toNamed(AuthRoutes.CONTACTS_PAGE, arguments: accessGranted);

        if (accessGranted) await _loadContacts();
      },
    );
  }

  Future<void> toggleFriendRequest(String userId) async {
    await runWithLoading(
      () async {
        if (state.pendingContactIds.contains(userId)) {
          await _cancelFriendRequest(userId);
        } else {
          await _sendFriendRequest(userId);
        }
      },
    );
  }

  UserRelationshipStatusEnum? getRelationshipStatus(String userId) {
    if (state.pendingContactIds.contains(userId)) {
      return UserRelationshipStatusEnum.pending;
    }
    return null;
  }

  void clearContactsData() {
    state.pendingContactIds.clear();
    state.getContactsReturn = null;
  }

  Future<void> _loadContacts() async {
    await runWithLoading(
      () async {
        // Read device contacts and extract phone numbers
        final phoneNumbers = await _getDeviceContactPhoneNumbers();

        state.getContactsReturn = await authService.usersApi.getContactsByUserId(
          sessionUser.sessionId,
          GetContactsByUserIdDto(phoneNumbers: phoneNumbers),
        );
      },
    );
  }

  /// Reads device contacts and extracts phone numbers
  Future<List<String>> _getDeviceContactPhoneNumbers() async {
    try {
      // Check if contacts permission is granted
      if (!await FlutterContacts.requestPermission()) return [];

      // Get all contacts with phone numbers
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );

      return contacts.map((contact) => contact.phones.map((phone) => phone.number).toList()).expand((x) => x).toList();
    } catch (e) {
      print('Error reading device contacts: $e');
      return [];
    }
  }

  Future<void> _sendFriendRequest(String userId) async {
    await runWithLoading(
      () async {
        await authService.userRelationshipsApi.inviteFriendByUserId(userId);
        state.pendingContactIds.add(userId);
      },
    );
  }

  Future<void> _cancelFriendRequest(String userId) async {
    await runWithLoading(
      () async {
        await authService.userRelationshipsApi.removeFriendByUserId(userId);
        state.pendingContactIds.remove(userId);
      },
    );
  }
}
