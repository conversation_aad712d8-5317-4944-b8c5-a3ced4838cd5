import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_state_manager.dart';
import 'package:ivent_app/features/auth/controllers/base_auth_controller.dart';
import 'package:ivent_app/routes/auth.dart';

class ValidationController extends BaseAuthController {
  ValidationController(AuthService authService, AuthStateManager state) : super(authService, state);

  bool get isValidationCodeComplete => state.validationCode.length == 6;

  Future<void> validateUser() async {
    await runWithLoading(
      () async {
        await Future.delayed(const Duration(seconds: 5));
        state.validateReturn = await authService.authApi.validate(
          ValidateDto(
            validationCode: state.validationCode,
            phoneNumber: state.formattedPhoneNumber,
          ),
        );

        if (state.validateReturn == null) {
          goToSomethingWentWrongPage();
          return;
        }

        if (state.validateReturn!.type == AuthEnum.login) {
          await _loginUser();
          Get.toNamed(AuthRoutes.APP_NAVIGATION);
        } else {
          Get.toNamed(AuthRoutes.NAME_PAGE);
        }
      },
    );
  }

  Future<void> _loginUser() async {
    final result = state.validateReturn!;
    final sessionUser = SessionUser(
      token: result.token!,
      sessionId: result.userId!,
      sessionRole: result.role!,
      sessionUsername: result.username!,
      sessionFullname: result.fullname!,
      sessionAvatarUrl: result.avatarUrl,
    );
    await authService.login(sessionUser);
  }
}
