import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_controller.dart';
import 'package:ivent_app/features/auth/widgets/common/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/form/validation_code_widget.dart';

class ValidatePhone extends StatefulWidget {
  const ValidatePhone({super.key});

  @override
  State<ValidatePhone> createState() => _ValidatePhoneState();
}

class _ValidatePhoneState extends State<ValidatePhone> {
  late final AuthController _controller;

  bool _canContinueToNextPage = false;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<AuthController>();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          const AuthInfoTextWidget(text: AuthStrings.koduGirebilirsin),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          ValidationCodeWidget(
            onCompleted: _handleCodeCompleted,
            onEditingChanged: _handleEditingChanged,
            onValidationChanged: _handleValidationChanged,
          ),
          const Spacer(),
          Obx(() {
            return IaFloatingActionButton(
              isEnabled: _canContinueToNextPage,
              isLoading: _controller.isLoading,
              text: AuthStrings.devamEt,
              onPressed: _handleContinuePressed,
            );
          }),
        ],
      ),
    );
  }

  void _handleCodeCompleted(String code) {
    _controller.state.validationCode = code;
  }

  void _handleEditingChanged(bool isEditing) {
    _isEditing = isEditing;
    if (isEditing) _canContinueToNextPage = false;
    setState(() {});
  }

  void _handleValidationChanged(bool isValid) {
    _canContinueToNextPage = isValid && !_isEditing;
    setState(() {});
  }

  Future<void> _handleContinuePressed() async {
    await _controller.validationController.validateUser();
  }
}
