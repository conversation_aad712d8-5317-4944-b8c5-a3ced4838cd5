import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/hobby_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/core/widgets/layout/screens/ia_search_screen.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_controller.dart';
import 'package:ivent_app/features/auth/widgets/hobby_category_box.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class RegistrationHobbiesView extends StatefulWidget {
  const RegistrationHobbiesView({super.key});

  @override
  State<RegistrationHobbiesView> createState() => _RegistrationHobbiesViewState();
}

class _RegistrationHobbiesViewState extends State<RegistrationHobbiesView> {
  late final AuthController _controller;

  late final TextEditingController _searchBarController;

  bool _canContinueToNextPage = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      showBackButton: false,
      title: AuthStrings.ilgiAlani,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppDimensions.padding20),
          _buildHintText(),
          Expanded(child: _buildSearchScreen()),
        ],
      ),
      floatingActionButton: IaFloatingActionButton(
        isEnabled: _canContinueToNextPage,
        text: AuthStrings.devamEt,
        onPressed: _handleContinuePressed,
      ),
    );
  }

  Widget _buildHintText() {
    return const Text(
      AuthStrings.ilgiAlaniText,
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey,
      ),
      maxLines: null,
    );
  }

  Widget _buildSearchScreen() {
    return IaSearchScreen(
      textEditingController: _searchBarController,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_selectedHobbyIds.isNotEmpty) _buildSelectedHobbies(),
          Expanded(child: _buildAvailableHobbies()),
        ],
      ),
    );
  }

  Widget _buildSelectedHobbies() {
    return Container(
      margin: const EdgeInsets.only(bottom: AuthDimensions.formElementSpacing),
      height: AppDimensions.buttonHeightSelectedHobbyTag,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: _selectedHobbyIds.length,
        itemBuilder: (context, index) => HobbyButtons.selectedHobbyTag(
          onTap: () => _handleHobbyToggle(_selectedHobbyIds[index]),
          text: Hobby.getHobbyNameFromHobbyId(_selectedHobbyIds[index]),
        ),
        separatorBuilder: (context, index) => const SizedBox(width: AuthDimensions.relatedElementSpacing),
      ),
    );
  }

  Widget _buildAvailableHobbies() {
    final categories = _availableHobbies.keys.toList();
    final hobbyLists = _availableHobbies.values.toList();

    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 100),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        return HobbyCategoryBox(
          mainCategory: categories.elementAt(index),
          hobbyList: hobbyLists.elementAt(index),
          selectedHobbyIds: _selectedHobbyIds,
          onHobbyToggle: _handleHobbyToggle,
        );
      },
      separatorBuilder: (context, index) => const SizedBox(height: AuthDimensions.hobbyCategorySpacing),
    );
  }

  List<String> get _selectedHobbyIds => _controller.state.checkedHobbyIds;

  Map<String, List<Hobby>> get _availableHobbies => {
        'Müzik': Hobby.hobbyListByParentHobbyName['Müzik']!,
        'Sanat & Kültür': Hobby.hobbyListByParentHobbyName['Sanat & Kültür']!,
        'Spor': Hobby.hobbyListByParentHobbyName['Spor']!,
        'Kariyer & Akademik': Hobby.hobbyListByParentHobbyName['Kariyer & Akademik']!,
        'Yeme İçme': Hobby.hobbyListByParentHobbyName['Yeme İçme']!,
        'Toplum': Hobby.hobbyListByParentHobbyName['Toplum']!,
      };

  void _handleHobbyToggle(String hobbyId) {
    _controller.registrationController.toggleHobby(hobbyId);
    setState(() {
      _canContinueToNextPage = _controller.registrationController.areHobbiesValid;
    });
  }

  Future<void> _handleContinuePressed() async {
    if (!_canContinueToNextPage) {
      return;
    }

    try {
      await _controller.registrationController.registerUser();

      _controller.goToAccessPage();
    } catch (error) {
      print('Registration error: $error');
    }
  }

  void _initializeControllers() {
    _controller = Get.find<AuthController>();
    _searchBarController = TextEditingController();

    _canContinueToNextPage = _controller.registrationController.areHobbiesValid;
  }

  void _disposeControllers() {
    _searchBarController.dispose();
  }
}
