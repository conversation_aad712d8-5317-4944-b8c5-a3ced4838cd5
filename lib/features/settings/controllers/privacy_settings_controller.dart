import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class PrivacySettingsController extends BaseController {
  PrivacySettingsController() : super(Get.find<AuthService>());

  // BACKEND DATA: Blocked users list - TEK ÇALIŞAN API
  final RxList<UserListItem> blockedUsers = <UserListItem>[].obs;
  final RxBool isLoadingBlocked = false.obs;

  // Loading states

  @override
  void onInit() {
    super.onInit();
    loadBlockedUsersFromBackend();
  }

  /// BACKEND ENTEGRASYON: Blocked users'ı backend'den yükle (TEK ÇALIŞAN API)
  Future<void> loadBlockedUsersFromBackend() async {
    try {
      await loadBlockedUsers();
    } catch (e) {
      debugPrint('❌ [PrivacySettings] Error loading blocked users from backend: $e');
      Get.snackbar(
        'Hata',
        '<PERSON>gellenen kullanıcılar yüklenirken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {}
  }

  /// BACKEND: Blocked users listesini yükle - TEK ÇALIŞAN API
  Future<void> loadBlockedUsers() async {
    try {
      isLoadingBlocked.value = true;

      // BACKEND API CALL
      final response = await authService.userRelationshipsApi.getUserBlocklist();

      if (response != null && response.users.isNotEmpty) {
        blockedUsers.value = response.users;
        debugPrint('✅ [PrivacySettings] Loaded ${response.users.length} blocked users from backend');
      } else {
        blockedUsers.clear();
        debugPrint('📝 [PrivacySettings] No blocked users found');
      }
    } catch (e) {
      debugPrint('❌ [PrivacySettings] Error loading blocked users: $e');
      // Backend API henüz hazır değilse normal, hata gösterme
      blockedUsers.clear();
    } finally {
      isLoadingBlocked.value = false;
    }
  }

  /// BACKEND ENTEGRE: Unblock user - TEK ÇALIŞAN API
  Future<void> unblockUser(String userId, String username) async {
    try {
      // BACKEND API CALL
      await authService.userRelationshipsApi.unblockUserByUserId(userId);

      // Local state update
      blockedUsers.removeWhere((user) => user.userId == userId);

      Get.snackbar(
        'Başarılı',
        '$username engeli kaldırıldı',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('✅ [PrivacySettings] User $username unblocked successfully');
    } catch (e) {
      debugPrint('❌ [PrivacySettings] Error unblocking user: $e');
      Get.snackbar(
        'Hata',
        'Engel kaldırılırken hata oluştu.',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Refresh blocked users from backend
  Future<void> refreshPrivacySettings() async {
    await loadBlockedUsersFromBackend();
  }

  /// Get blocked users count
  int get blockedUsersCount => blockedUsers.length;
}
