import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';

class BlockedUsersPage extends StatelessWidget {
  const BlockedUsersPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PrivacySettingsController>();
    
    return IaScaffold.noSearch(
      title: 'Engellenen Kişiler',
      body: Obx(() {
        if (controller.isLoading ) {
          return const Center(child: CircularProgressIndicator(color: AppColors.primary));
        }
        
        if (controller.blockedUsers.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.block,
                  size: 64,
                  color: AppColors.grey400,
                ),
                const SizedBox(height: AppDimensions.padding16),
                Text(
                  'Engellenen kullanıcı yok',
                  style: AppTextStyles.size16Bold,
                ),
                const SizedBox(height: AppDimensions.padding8),
                Text(
                  'Henüz hiç kimseyi engellemediniz.',
                  style: AppTextStyles.size14Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        
        return ListView.builder(
          padding: const EdgeInsets.all(AppDimensions.padding20),
          itemCount: controller.blockedUsers.length,
          itemBuilder: (context, index) {
            final user = controller.blockedUsers[index];
            return Container(
              margin: const EdgeInsets.only(bottom: AppDimensions.padding12),
              padding: const EdgeInsets.all(AppDimensions.padding16),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.grey200, width: 1),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: AppColors.grey200,
                    backgroundImage: user.avatarUrl != null ? NetworkImage(user.avatarUrl!) : null,
                    child: user.avatarUrl == null
                        ? const Icon(Icons.person, color: AppColors.grey600)
                        : null,
                  ),
                  const SizedBox(width: AppDimensions.padding16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.username,
                          style: AppTextStyles.size14Bold,
                        ),
                        if (user.university != null) ...[
                          const SizedBox(height: AppDimensions.padding4),
                          Text(
                            user.university!,
                            style: AppTextStyles.size12Regular.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(width: AppDimensions.padding8),
                  ElevatedButton(
                    onPressed: () => _showUnblockDialog(context, controller, user.userId, user.username),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: AppColors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.padding16,
                        vertical: AppDimensions.padding8,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                    ),
                    child: Text(
                      'Engeli Kaldır',
                      style: AppTextStyles.size12Bold.copyWith(color: AppColors.white),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      }),
    );
  }

  void _showUnblockDialog(BuildContext context, PrivacySettingsController controller, String userId, String username) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text(
          'Engeli Kaldır',
          style: AppTextStyles.size16Bold,
        ),
        content: Text(
          '$username kullanıcısının engelini kaldırmak istediğinizden emin misiniz?',
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'İptal',
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.unblockUser(userId, username);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
            child: Text(
              'Engeli Kaldır',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }
} 