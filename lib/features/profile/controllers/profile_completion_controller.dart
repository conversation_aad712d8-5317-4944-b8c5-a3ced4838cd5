import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_side_menu_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';

/// Controller for managing profile completion functionality
/// Extends ProfileController to access all profile-related functionality
class ProfileCompletionController extends ProfileController {
  // Profile completion specific state
  final profileImage = Rxn<File>();
  final birthDateText = 'GG/AA/YYYY'.obs;
  final selectedGender = ''.obs;
  final currentFullname = ''.obs;
  final currentAvatarUrl = ''.obs;

  // Form controllers
  late TextEditingController usernameController;

  // Dependencies
  final ImagePicker _imagePicker = ImagePicker();
  DateTime? _selectedDate;
  String? _base64ProfileImage;

  // Current user data
  GetUserByUserIdReturn? _currentUserData;

  // Constructor
  ProfileCompletionController(AuthService authService, ProfileStateManager state) : super(authService, state);

  @override
  void initController() async {
    super.initController();
    usernameController = TextEditingController();
    await _loadCurrentUserData();
  }

  @override
  void closeController() {
    usernameController.dispose();
    super.closeController();
  }

  /// Loads current user data from API using OpenAPI generated endpoint
  Future<void> _loadCurrentUserData() async {
    try {
      debugPrint('🔄 [DEBUG] Loading current user data from API...');
      debugPrint('🆔 [DEBUG] User ID: ${sessionUser.sessionId}');

      // Get current user data using OpenAPI generated endpoint
      _currentUserData = await authService.usersApi.getByUserId(sessionUser.sessionId);

      if (_currentUserData != null) {
        debugPrint('✅ [DEBUG] User data loaded successfully:');
        debugPrint('  - Username: ${_currentUserData!.username}');
        debugPrint('  - Fullname: ${_currentUserData!.fullname}');
        debugPrint('  - Avatar URL: ${_currentUserData!.avatarUrl}');
        debugPrint('  - User Role: ${_currentUserData!.userRole}');

        // Set form fields with current data from API
        usernameController.text = _currentUserData!.username;

        // Set current fullname from API data
        currentFullname.value = _currentUserData!.fullname;

        // Set current avatar URL from API data
        if (_currentUserData!.avatarUrl != null && _currentUserData!.avatarUrl!.isNotEmpty) {
          currentAvatarUrl.value = _currentUserData!.avatarUrl!;
          debugPrint('🖼️ [DEBUG] Avatar URL set: ${currentAvatarUrl.value}');
        } else {
          debugPrint('🖼️ [DEBUG] No avatar URL found in API data');
        }

        // Try to get existing profile data if available
        await _loadExistingProfileData();
      } else {
        debugPrint('❌ [DEBUG] No user data returned from API');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error loading user data: $e');
      debugPrint('❌ [DEBUG] Error type: ${e.runtimeType}');

      Get.snackbar(
        'Hata',
        'Kullanıcı bilgileri yüklenirken bir hata oluştu: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      debugPrint('🏁 [DEBUG] User data loading completed');
    }
  }

  /// Loads existing profile data if available
  Future<void> _loadExistingProfileData() async {
    try {
      // Note: Since GetUserByUserIdReturn doesn't have gender and birthday fields,
      // we'll check if the user has completed their profile before
      // For now, we'll start with empty values for first-time completion

      // All profile data is loaded from API via GetUserByUserIdReturn
      // Additional profile fields (gender, birthday) will be populated
      // when user completes the profile for the first time

      // No need to use SessionUser data - everything comes from API
    } catch (e) {
      // Silent error - user can still complete their profile
      debugPrint('Loading existing profile data error: $e');
    }
  }

  /// Opens image picker to select profile image and converts to base64
  Future<void> pickImage() async {
    try {
      debugPrint('📸 [DEBUG] Opening image picker...');

      final pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1080,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (pickedImage != null) {
        debugPrint('📸 [DEBUG] Image selected: ${pickedImage.path}');
        profileImage.value = File(pickedImage.path);

        // Convert image to base64 for API upload
        final bytes = await File(pickedImage.path).readAsBytes();
        _base64ProfileImage = base64Encode(bytes);

        final fileSizeKB = bytes.length / 1024;
        final base64SizeKB = _base64ProfileImage!.length / 1024;

        debugPrint('📸 [DEBUG] Image processed successfully:');
        debugPrint('  - Original file size: ${fileSizeKB.toStringAsFixed(2)} KB');
        debugPrint('  - Base64 data size: ${base64SizeKB.toStringAsFixed(2)} KB');
        debugPrint('  - Base64 ready for API upload');
        debugPrint('  - Preview: data:image/jpeg;base64,${_base64ProfileImage!.substring(0, 50)}...');
      } else {
        debugPrint('📸 [DEBUG] No image selected');
      }
    } catch (e) {
      debugPrint('❌ [DEBUG] Error picking image: $e');

      Get.snackbar(
        'Hata',
        'Fotoğraf seçilirken bir hata oluştu: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Opens date picker to select birth date
  Future<void> pickBirthDate() async {
    try {
      final DateTime now = DateTime.now();
      final DateTime minimumDate = DateTime(now.year - 100);
      final DateTime maximumDate = DateTime(now.year - 13, now.month, now.day);

      final DateTime? picked = await showDatePicker(
        context: Get.context!,
        initialDate: _selectedDate ?? DateTime(now.year - 18),
        firstDate: minimumDate,
        lastDate: maximumDate,
        locale: const Locale('tr', 'TR'),
      );

      if (picked != null) {
        _selectedDate = picked;
        birthDateText.value = _formatDate(picked);
      }
    } catch (e) {
      Get.snackbar(
        'Hata',
        'Tarih seçilirken bir hata oluştu: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Gets display text for a gender enum value
  String _getGenderDisplayText(UserGenderEnum gender) {
    switch (gender) {
      case UserGenderEnum.male:
        return 'Erkek';
      case UserGenderEnum.female:
        return 'Kadın';
      case UserGenderEnum.nonBinary:
        return 'Non-Binary';
      case UserGenderEnum.other:
        return 'Diğer';
      case UserGenderEnum.preferNotToSay:
        return 'Belirtmek İstemiyorum';
      default:
        return 'Belirtmek İstemiyorum';
    }
  }

  /// Gets gender enum from display text
  UserGenderEnum _getGenderEnumFromDisplayText(String displayText) {
    for (final gender in UserGenderEnum.values) {
      if (_getGenderDisplayText(gender) == displayText) {
        return gender;
      }
    }
    return UserGenderEnum.preferNotToSay; // fallback
  }

  /// Shows gender selection bottom sheet
  void showGenderPicker() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Cinsiyet Seçin',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Use OpenAPI generated enum values
            ...UserGenderEnum.values.map((gender) => _buildGenderOption(_getGenderDisplayText(gender), gender)),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// Builds a gender option for the bottom sheet
  Widget _buildGenderOption(String label, UserGenderEnum gender) {
    return ListTile(
      title: Text(label),
      trailing: selectedGender.value == label ? const Icon(Icons.check, color: Colors.blue) : null,
      onTap: () {
        selectedGender.value = label;
        Get.back();
      },
    );
  }

  /// Validates the form data
  String? _validateForm() {
    debugPrint('🔍 [DEBUG] Validating form data...');
    debugPrint('  - Username: "${usernameController.text}"');
    debugPrint('  - Selected date: $_selectedDate');
    debugPrint('  - Selected gender: "${selectedGender.value}"');
    debugPrint('  - Has profile image: ${profileImage.value != null}');
    debugPrint('  - Current avatar URL: "${currentAvatarUrl.value}"');

    if (usernameController.text.isEmpty) {
      debugPrint('❌ [DEBUG] Validation failed: Username is empty');
      return 'Lütfen kullanıcı adı giriniz.';
    }

    if (usernameController.text.length < 3) {
      debugPrint('❌ [DEBUG] Validation failed: Username too short (${usernameController.text.length} chars)');
      return 'Kullanıcı adı en az 3 karakter olmalıdır.';
    }

    if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(usernameController.text)) {
      debugPrint('❌ [DEBUG] Validation failed: Username contains invalid characters');
      return 'Kullanıcı adı sadece harf, rakam, alt çizgi ve tire içerebilir.';
    }

    if (_selectedDate == null) {
      debugPrint('❌ [DEBUG] Validation failed: No birth date selected');
      return 'Lütfen doğum tarihinizi seçiniz.';
    }

    if (selectedGender.value.isEmpty) {
      debugPrint('❌ [DEBUG] Validation failed: No gender selected');
      return 'Lütfen cinsiyetinizi seçiniz.';
    }

    debugPrint('✅ [DEBUG] Form validation passed');
    return null;
  }

  /// Saves the profile information using OpenAPI generated endpoints
  Future<void> saveProfile() async {
    final validationError = _validateForm();
    if (validationError != null) {
      debugPrint('🚨 [DEBUG] Form validation failed: $validationError');
      Get.snackbar(
        'Uyarı',
        validationError,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      return;
    }

    try {
      debugPrint('🔄 [DEBUG] Starting profile save process...');

      // Determine avatar URL - use base64 image data if available, otherwise keep existing
      String? avatarUrl = currentAvatarUrl.value.isNotEmpty ? currentAvatarUrl.value : null;

      // If user selected a new image, send base64 data
      if (_base64ProfileImage != null) {
        // Send base64 data directly as avatar URL
        avatarUrl = 'data:image/jpeg;base64,$_base64ProfileImage';
        debugPrint('🖼️ [DEBUG] User selected new image, sending base64 data');
        debugPrint('🖼️ [DEBUG] Base64 data length: ${_base64ProfileImage!.length} characters');
      } else {
        debugPrint('🖼️ [DEBUG] No new image selected, keeping existing avatar URL: $avatarUrl');
      }

      // Format birthday as ISO 8601 date-time (required by API)
      // API strictly requires full ISO 8601 date-time format with timezone
      // Format: YYYY-MM-DDTHH:MM:SSZ (using midnight UTC)
      final formattedBirthday =
          '${_selectedDate!.year.toString().padLeft(4, '0')}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}T00:00:00Z';

      debugPrint('📅 [DEBUG] Original date: ${_selectedDate!}');
      debugPrint('📅 [DEBUG] Formatted birthday: $formattedBirthday');

      // Prepare update data using OpenAPI generated DTO
      final updateDto = UpdateByUserIdDto(
        newUsername: usernameController.text,
        newBirthday: formattedBirthday,
        newGender: _getGenderEnumFromDisplayText(selectedGender.value),
        newAvatarUrl: avatarUrl ?? '', // avatarUrl is already nullable
      );

      debugPrint('📦 [DEBUG] Update DTO created:');
      debugPrint('  - Username: ${updateDto.newUsername}');
      debugPrint('  - Birthday: ${updateDto.newBirthday}');
      debugPrint('  - Gender: ${updateDto.newGender}');
      if (avatarUrl != null) {
        // Check if avatarUrl is not null
        if (avatarUrl.startsWith('data:image/')) {
          debugPrint('  - Avatar: Base64 data (${avatarUrl.length} characters)');
          debugPrint('  - Avatar type: ${avatarUrl.substring(0, avatarUrl.indexOf(';'))}');
        } else {
          debugPrint('  - Avatar URL: $avatarUrl');
        }
      } else {
        debugPrint('  - Avatar: No change (using existing or null)');
      }

      // Update profile using OpenAPI generated endpoint
      debugPrint('🌐 [DEBUG] Calling API: updateByUserId with userId: ${sessionUser.sessionId}');
      await authService.usersApi.updateByUserId(
        sessionUser.sessionId,
        updateDto,
      );

      debugPrint('✅ [DEBUG] Profile update successful');
      debugPrint('⚠️ [DEBUG] API returned void - this is expected for update operations');
      debugPrint('🔍 [DEBUG] Now checking if backend actually processed the data...');

      // Refresh user data after successful update
      await _refreshUserSession();

      // Refresh side menu data if controller exists
      await _refreshSideMenuData();

      // Show success message
      Get.snackbar(
        'Başarılı',
        'Profil bilgileriniz başarıyla güncellendi.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 5), // Daha uzun süre
      );

      // Wait longer for the snackbar to be visible, then navigate back
      await Future.delayed(const Duration(milliseconds: 2000)); // 2 saniye bekle
      Get.back();
    } catch (e) {
      debugPrint('❌ [DEBUG] Profile save error: $e');
      debugPrint('❌ [DEBUG] Error type: ${e.runtimeType}');

      String errorMessage = 'Profil güncellenirken bir hata oluştu.';

      if (e is ApiException) {
        debugPrint('❌ [DEBUG] API Exception details:');
        debugPrint('  - Status Code: ${e.code}');
        debugPrint('  - Message: ${e.message}');

        // Parse common API validation errors
        if (e.code == 400) {
          if (e.message?.contains('username') == true) {
            errorMessage = 'Kullanıcı adı zaten kullanımda veya geçersiz format.';
          } else if (e.message?.contains('birthday') == true) {
            errorMessage = 'Doğum tarihi formatı geçersiz.';
          } else if (e.message?.contains('gender') == true) {
            errorMessage = 'Cinsiyet bilgisi geçersiz.';
          } else if (e.message?.contains('avatarUrl') == true) {
            errorMessage = 'Profil fotoğrafı URL\'si geçersiz.';
          } else {
            errorMessage = 'Girilen bilgiler geçersiz. Lütfen kontrol edip tekrar deneyin.';
          }
        } else if (e.code == 401) {
          errorMessage = 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.';
        } else if (e.code == 403) {
          errorMessage = 'Bu işlem için yetkiniz bulunmuyor.';
        } else if (e.code == 404) {
          errorMessage = 'Kullanıcı bulunamadı.';
        } else if (e.code == 500) {
          errorMessage = 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.';
        }

        debugPrint('❌ [DEBUG] Parsed error message: $errorMessage');
      } else {
        debugPrint('❌ [DEBUG] Non-API Exception: $e');
        errorMessage = 'Bağlantı hatası. İnternet bağlantınızı kontrol edin.';
      }

      Get.snackbar(
        'Hata',
        errorMessage,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    } finally {
      debugPrint('🏁 [DEBUG] Profile save process completed');
    }
  }

  /// Refreshes user session data after profile update using OpenAPI
  Future<void> _refreshUserSession() async {
    try {
      debugPrint('🔄 [DEBUG] Refreshing user session...');

      // Get updated user data using OpenAPI generated endpoint
      final updatedUserData = await authService.usersApi.getByUserId(sessionUser.sessionId);

      if (updatedUserData != null) {
        debugPrint('✅ [DEBUG] Updated user data received:');
        debugPrint('  - Username: ${updatedUserData.username}');
        debugPrint('  - User Role: ${updatedUserData.userRole}');
        debugPrint('  - Avatar URL: ${updatedUserData.avatarUrl}');
        debugPrint('  - Full API Response: ${updatedUserData.toString()}');

        // Check avatar URL format
        if (updatedUserData.avatarUrl != null) {
          if (updatedUserData.avatarUrl!.startsWith('data:image/')) {
            debugPrint('🖼️ [DEBUG] Avatar returned as base64 data');
            debugPrint('  - Data length: ${updatedUserData.avatarUrl!.length} characters');
          } else {
            debugPrint('🖼️ [DEBUG] Avatar returned as URL: ${updatedUserData.avatarUrl}');
          }
        } else {
          debugPrint('🖼️ [DEBUG] No avatar URL returned from backend');
        }

        // CRITICAL: Check if birthday and gender are present in the response
        debugPrint('🔍 [DEBUG] BACKEND DATA CHECK:');
        debugPrint('  - Birthday in response: ${updatedUserData.toString().contains('birthday') ? 'YES' : 'NO'}');
        debugPrint('  - Gender in response: ${updatedUserData.toString().contains('gender') ? 'YES' : 'NO'}');
        debugPrint('  - RelationshipStatus: ${updatedUserData.relationshipStatus}');
        debugPrint('');
        debugPrint('🚨 [CRITICAL] API MODEL INCONSISTENCY DETECTED:');
        debugPrint('  - Update API accepts birthday/gender fields');
        debugPrint('  - Get User API does NOT return birthday/gender fields');
        debugPrint('  - This is a backend API design issue');
        debugPrint('  - Profile completion cannot be properly validated');

        // Check if level changed after profile update
        final oldLevel = sessionUser.sessionRole;
        final newLevel = updatedUserData.userRole;

        if (oldLevel != newLevel) {
          debugPrint('🎉 [DEBUG] Level changed from $oldLevel to $newLevel');
        } else {
          debugPrint('⚠️ [DEBUG] Level did NOT change - still $oldLevel');
          debugPrint('🔍 [DEBUG] This suggests backend did not process profile completion properly');
        }

        // Create new session user with updated data
        final newSessionUser = SessionUser(
          token: sessionUser.token,
          sessionId: updatedUserData.userId,
          sessionRole: updatedUserData.userRole,
          sessionUsername: updatedUserData.username,
          sessionFullname: updatedUserData.fullname,
          sessionAvatarUrl: updatedUserData.avatarUrl,
        );

        // Update session using AuthService
        await authService.login(newSessionUser);
        debugPrint('✅ [DEBUG] Session updated successfully');
      }
    } catch (e) {
      // Silent error - user will see changes on next app restart
      debugPrint('❌ [DEBUG] Session refresh error: $e');
    }
  }

  /// Refreshes side menu data after profile update
  Future<void> _refreshSideMenuData() async {
    try {
      // Check if ProfileSideMenuController exists and refresh it
      if (Get.isRegistered<ProfileSideMenuController>(tag: 'sideMenu')) {
        final sideMenuController = Get.find<ProfileSideMenuController>(tag: 'sideMenu');
        await sideMenuController.refreshSideMenuData();
        debugPrint('✅ [DEBUG] Side menu data refreshed successfully');
      } else {
        debugPrint('ℹ️ [DEBUG] Side menu controller not found, skipping refresh');
      }
    } catch (e) {
      debugPrint('⚠️ [DEBUG] Failed to refresh side menu data: $e');
    }
  }

  // Helper methods

  /// Formats date to display format
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Getters for current user data using OpenAPI models
  String get currentUsername => _currentUserData?.username ?? '';
  UserRoleEnum get currentUserRole => _currentUserData?.userRole ?? UserRoleEnum.level0;

  // Additional getters for profile completion status
  bool get hasProfileImage => profileImage.value != null || currentAvatarUrl.value.isNotEmpty;
  bool get hasSelectedBirthDate => _selectedDate != null;
  bool get hasSelectedGender => selectedGender.value.isNotEmpty;
  bool get isProfileComplete =>
      hasProfileImage && hasSelectedBirthDate && hasSelectedGender && usernameController.text.isNotEmpty;
}
